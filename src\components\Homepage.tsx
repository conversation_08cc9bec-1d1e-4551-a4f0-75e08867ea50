import React, { useRef, useState, useEffect } from 'react';
import './Homepage.css';

/**
 * Interface untuk data produk carousel
 * Mendefinisikan struktur data untuk setiap item produk
 */
interface ProductItem {
  id: number;
  image: string;
  title: string;
  topic: string;
  description: string;
  detailTitle: string;
  detailDescription: string;
  specifications: {
    usedTime: string;
    chargingPort: string;
    compatible: string;
    bluetooth: string;
    controlled: string;
  };
}

/**
 * Komponen Homepage dengan carousel produk
 * Menampilkan slider produk dengan animasi dan detail produk
 */
const Homepage: React.FC = () => {
  // State untuk mengontrol carousel
  const [isShowingDetail, setIsShowingDetail] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  // State untuk date/time
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // Refs untuk manipulasi DOM
  const carouselRef = useRef<HTMLDivElement>(null);
  const listRef = useRef<HTMLDivElement>(null);

  // Update date/time setiap detik
  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, []);

  // Helper functions untuk format date/time
  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('id-ID', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    });
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('id-ID', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  // Data produk untuk carousel
  const products: ProductItem[] = [
    {
      id: 1,
      image: '/images/product/img1.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    },
    {
      id: 2,
      image: '/images/product/img2.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    },
    {
      id: 3,
      image: '/images/product/img3.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    },
    {
      id: 4,
      image: '/images/product/img4.png',
      title: 'COMPRESSOR AC HD',
      topic: 'MODEL A',
      description: 'Kami menawarkan compressor dengan harga terjangkau namun kualitas premium.',
      detailTitle: 'COMPRESSOR AC HD MODEL A',
      detailDescription: 'Compressor dengan teknologi AI-powered audio enhancement dan adaptive EQ. Memberikan pengalaman audio yang dipersonalisasi sesuai dengan preferensi pengguna.',
      specifications: {
        usedTime: '15.000 psi',
        chargingPort: 'Type-C',
        compatible: 'Universal',
        bluetooth: '5.4',
        controlled: '-'
      }
    }
  ];

  /**
   * Fungsi untuk menggeser carousel ke slide berikutnya atau sebelumnya
   * @param direction - 'next' untuk slide berikutnya, 'prev' untuk slide sebelumnya
   */
  const showSlider = (direction: 'next' | 'prev') => {
    if (isAnimating || !listRef.current || !carouselRef.current) return;

    setIsAnimating(true);

    const items = listRef.current.querySelectorAll('.carousel-item') as any;
    const carousel = carouselRef.current as any;

    // Hapus kelas animasi sebelumnya
    carousel.classList.remove('next', 'prev');

    if (direction === 'next') {
      // Pindahkan item pertama ke akhir
      listRef.current.appendChild(items[0]);
      carousel.classList.add('next');
    } else {
      // Pindahkan item terakhir ke awal
      listRef.current.prepend(items[items.length - 1]);
      carousel.classList.add('prev');
    }

    // Reset animasi setelah 2 detik
    setTimeout(() => {
      setIsAnimating(false);
    }, 2000);
  };

  /**
   * Fungsi untuk menampilkan detail produk
   */
  const showDetail = () => {
    if (!carouselRef.current) return;

    (carouselRef.current as any).classList.remove('next', 'prev');
    (carouselRef.current as any).classList.add('showDetail');
    setIsShowingDetail(true);
  };

  /**
   * Fungsi untuk kembali ke tampilan carousel
   */
  const hideDetail = () => {
    if (!carouselRef.current) return;

    (carouselRef.current as any).classList.remove('showDetail');
    setIsShowingDetail(false);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/10 via-secondary/5 to-accent/10 text-base-content overflow-hidden">
      {/* Header dengan logo perusahaan */}
      <header className="w-full max-w-7xl mx-auto px-4 py-3 flex items-center justify-start">
        <div className="flex items-center gap-4">
          <img
            src="/images/icons/icon-192x192.png"
            alt="PT Putera Wibowo Borneo Logo"
            className="w-10 h-10 object-contain rounded-lg shadow-lg shadow-primary/20"
          />
          <h1 className="text-xl font-semibold text-primary tracking-wide">
            PT Putera Wibowo Borneo
          </h1>
        </div>
      </header>

      {/* Main Content Area - Responsive Grid Layout */}
      <div className="flex-1 h-[calc(100vh-80px)] relative">
        {/* Tablet Landscape Layout (1024px+) */}
        <div className="hidden lg:grid lg:grid-cols-2 h-full">
          {/* Left Section - Date/Time (50% width) */}
          <div className="flex flex-col items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20 text-base-content relative overflow-hidden">
            <div className="text-center z-10 p-8 datetime-display">
              <div className="text-6xl font-bold text-primary mb-4 datetime-time">
                {formatTime(currentDateTime)}
              </div>
              <div className="text-2xl font-medium text-base-content/80 leading-relaxed datetime-date">
                {formatDate(currentDateTime)}
              </div>
            </div>
            {/* Decorative background elements */}
            <div className="absolute top-10 left-10 w-32 h-32 bg-primary/10 rounded-full blur-xl"></div>
            <div className="absolute bottom-10 right-10 w-24 h-24 bg-secondary/10 rounded-full blur-lg"></div>
          </div>

          {/* Right Section - Product Carousel (50% width) */}
          <div className="relative bg-base-100">
            {/* Product Section Content */}
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRef} className="carousel h-full">
                <div ref={listRef} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          SEE MORE &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.usedTime}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.chargingPort}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatible}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.bluetooth}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.controlled}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}
                  >
                    See All &#8599;
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Tablet Portrait Layout (768px - 1023px) */}
        <div className="hidden md:flex lg:hidden flex-col h-full">
          {/* Top Section - Date/Time (40% height) */}
          <div className="h-2/5 flex flex-col items-center justify-center bg-gradient-to-br from-primary/20 to-secondary/20 text-base-content relative overflow-hidden">
            <div className="text-center z-10 p-6 datetime-display">
              <div className="text-4xl font-bold text-primary mb-3 datetime-time">
                {formatTime(currentDateTime)}
              </div>
              <div className="text-lg font-medium text-base-content/80 leading-relaxed datetime-date">
                {formatDate(currentDateTime)}
              </div>
            </div>
            {/* Decorative background elements */}
            <div className="absolute top-5 left-5 w-20 h-20 bg-primary/10 rounded-full blur-lg"></div>
            <div className="absolute bottom-5 right-5 w-16 h-16 bg-secondary/10 rounded-full blur-md"></div>
          </div>

          {/* Bottom Section - Product Carousel (60% height) */}
          <div className="h-3/5 relative bg-base-100">
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRef} className="carousel h-full">
                <div ref={listRef} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          SEE MORE &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.usedTime}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.chargingPort}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatible}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.bluetooth}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.controlled}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}
                  >
                    See All &#8599;
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Mobile Layout (< 768px) */}
        <div className="md:hidden h-full">
          {/* Mobile uses existing carousel layout with improved styling */}
          <div className="h-full relative bg-base-100">
            <div className="h-full relative overflow-hidden">
              {/* Carousel utama */}
              <div ref={carouselRef} className="carousel h-full">
                <div ref={listRef} className="carousel-list">
                  {products.map((product) => (
                    <div key={product.id} className="carousel-item">
                      <img src={product.image} alt={product.topic} className="product-image" />

                      {/* Konten perkenalan produk */}
                      <div className="introduce">
                        <div className="title text-base-content">{product.title}</div>
                        <div className="topic text-base-content">{product.topic}</div>
                        <div className="description text-base-content/70">{product.description}</div>
                        <button className="see-more-btn text-base-content hover:text-primary" onClick={showDetail}>
                          SEE MORE &#8599;
                        </button>
                      </div>

                      {/* Detail produk */}
                      <div className="detail">
                        <div className="detail-title text-base-content">{product.detailTitle}</div>
                        <div className="detail-description text-base-content/80">{product.detailDescription}</div>

                        {/* Spesifikasi produk */}
                        <div className="specifications">
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Used Time</p>
                            <p className="text-base-content/70">{product.specifications.usedTime}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Charging Port</p>
                            <p className="text-base-content/70">{product.specifications.chargingPort}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Compatible</p>
                            <p className="text-base-content/70">{product.specifications.compatible}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Bluetooth</p>
                            <p className="text-base-content/70">{product.specifications.bluetooth}</p>
                          </div>
                          <div className="spec-item">
                            <p className="text-base-content font-semibold">Controlled</p>
                            <p className="text-base-content/70">{product.specifications.controlled}</p>
                          </div>
                        </div>

                        {/* Tombol checkout */}
                        <div className="checkout">
                          <button className="btn btn-outline">ADD TO CART</button>
                          <button className="btn btn-primary">CHECKOUT</button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Tombol navigasi */}
                <div className="arrows">
                  <button
                    id="prev"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('prev')}
                    disabled={isAnimating}
                  >
                    &#8249;
                  </button>
                  <button
                    id="next"
                    className="nav-btn bg-base-100/80 hover:bg-primary hover:text-primary-content border border-base-300 text-base-content"
                    onClick={() => showSlider('next')}
                    disabled={isAnimating}
                  >
                    &#8250;
                  </button>
                  <button
                    id="back"
                    className={`back-btn text-base-content hover:bg-base-200 ${isShowingDetail ? 'visible' : ''}`}
                    onClick={hideDetail}
                  >
                    See All &#8599;
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* CSS Shape Divider */}
        <div className="absolute bottom-0 left-0 w-full overflow-hidden leading-none">
          <svg
            className="relative block w-full h-24"
            xmlns="http://www.w3.org/2000/svg"
            viewBox="0 0 1200 120"
            preserveAspectRatio="none"
          >
            <path
              d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z"
              opacity=".25"
              className="fill-base-100"
            ></path>
            <path
              d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z"
              opacity=".5"
              className="fill-base-100"
            ></path>
            <path
              d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"
              className="fill-base-100"
            ></path>
          </svg>
        </div>
      </div>
    </div>
  );
};

export default Homepage;
