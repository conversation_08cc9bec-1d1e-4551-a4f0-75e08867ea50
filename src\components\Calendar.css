/* Calendar Component Styles - Responsive untuk semua ukuran layar */

/* Main container */
.calendar-container {
  width: 100%;
  height: 100vh;
  display: flex;
  gap: 20px;
  padding: 20px;
  background-color: var(--fallback-b1, oklch(var(--b1)));
  font-family: 'Poppins', sans-serif;
  overflow: hidden;
  position: relative;
  box-sizing: border-box;
}

/* Ensure all child elements use border-box */
.calendar-container *,
.calendar-container *::before,
.calendar-container *::after {
  box-sizing: border-box;
}

/* Main Calendar Section (70%) */
.main-calendar-section {
  flex: 0 0 70%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow: hidden;
}

/* Calendar Header */
.calendar-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10px;
}

.calendar-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0;
}

.nav-button {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  font-size: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.nav-button:hover {
  background: var(--fallback-s, oklch(var(--s)));
  transform: scale(1.1);
}

/* Calendar Grid */
.calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
  flex: 1;
  background: var(--fallback-b3, oklch(var(--b3)));
  border-radius: 12px;
  padding: 10px;
  overflow: hidden;
}

.day-header {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  padding: 10px;
  text-align: center;
  font-weight: 600;
  font-size: 0.9rem;
  border-radius: 6px;
}

.calendar-date {
  background: var(--fallback-b1, oklch(var(--b1)));
  padding: 15px 10px;
  text-align: center;
  cursor: pointer;
  border-radius: 6px;
  position: relative;
  transition: all 0.2s ease;
  min-height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.calendar-date:hover {
  background: var(--fallback-b2, oklch(var(--b2)));
  transform: scale(1.02);
}

.calendar-date.other-month {
  color: var(--fallback-bc, oklch(var(--bc) / 0.4));
  background: var(--fallback-b1, oklch(var(--b1) / 0.5));
}

.calendar-date.today {
  background: var(--fallback-a, oklch(var(--a)));
  color: var(--fallback-ac, oklch(var(--ac)));
  font-weight: 700;
  box-shadow: 0 0 0 2px var(--fallback-p, oklch(var(--p)));
}

.calendar-date.has-tasks {
  background: var(--fallback-s, oklch(var(--s) / 0.2));
  border: 2px solid var(--fallback-s, oklch(var(--s)));
}

.task-indicator {
  position: absolute;
  bottom: 5px;
  right: 5px;
  width: 8px;
  height: 8px;
  background: var(--fallback-s, oklch(var(--s)));
  border-radius: 50%;
}

/* Task List Section */
.task-list-section {
  flex: 0 0 200px;
  overflow-y: auto;
}

.task-list-section h3 {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0 0 15px 0;
}

.task-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.no-tasks {
  text-align: center;
  color: var(--fallback-bc, oklch(var(--bc) / 0.6));
  font-style: italic;
  padding: 20px;
}

.task-item {
  background: var(--fallback-b2, oklch(var(--b2)));
  padding: 15px;
  border-radius: 8px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  transition: all 0.2s ease;
}

.task-item:hover {
  background: var(--fallback-b3, oklch(var(--b3)));
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.task-info h4 {
  margin: 0 0 5px 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--fallback-bc, oklch(var(--bc)));
}

.task-info p {
  margin: 0;
  font-size: 0.85rem;
  color: var(--fallback-bc, oklch(var(--bc) / 0.7));
}

.task-actions {
  display: flex;
  align-items: center;
  gap: 10px;
}

.task-status {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1rem;
  font-weight: bold;
}

.task-status.pending {
  background: var(--fallback-w, oklch(var(--w)));
  color: var(--fallback-wc, oklch(var(--wc)));
}

.task-status.notified {
  background: var(--fallback-su, oklch(var(--su)));
  color: var(--fallback-suc, oklch(var(--suc)));
}

.delete-task-btn {
  background: var(--fallback-er, oklch(var(--er)));
  color: var(--fallback-erc, oklch(var(--erc)));
  border: none;
  border-radius: 50%;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.delete-task-btn:hover {
  background: var(--fallback-er, oklch(var(--er) / 0.8));
  transform: scale(1.1);
}

.delete-task-btn:active {
  transform: scale(0.95);
}

/* Secondary Section (30%) */
.secondary-section {
  flex: 0 0 30%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
}

/* Secondary Calendar */
.secondary-calendar {
  background: var(--fallback-b2, oklch(var(--b2)));
  padding: 15px;
  border-radius: 12px;
}

.secondary-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0 0 15px 0;
  text-align: center;
}

.secondary-calendar-grid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 2px;
}

.secondary-day-header {
  background: var(--fallback-p, oklch(var(--p) / 0.8));
  color: var(--fallback-pc, oklch(var(--pc)));
  padding: 5px;
  text-align: center;
  font-weight: 600;
  font-size: 0.7rem;
  border-radius: 4px;
}

.secondary-date {
  background: var(--fallback-b1, oklch(var(--b1)));
  padding: 8px 4px;
  text-align: center;
  border-radius: 4px;
  font-size: 0.8rem;
  min-height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.secondary-date:hover {
  background: var(--fallback-b3, oklch(var(--b3)));
}

.secondary-date.other-month {
  color: var(--fallback-bc, oklch(var(--bc) / 0.4));
  background: var(--fallback-b1, oklch(var(--b1) / 0.5));
}

.secondary-date.today {
  background: var(--fallback-a, oklch(var(--a)));
  color: var(--fallback-ac, oklch(var(--ac)));
  font-weight: 700;
}

.secondary-date.has-tasks {
  background: var(--fallback-s, oklch(var(--s) / 0.3));
  border: 1px solid var(--fallback-s, oklch(var(--s)));
}

/* Task Form Section */
.task-form-section {
  background: var(--fallback-b2, oklch(var(--b2)));
  padding: 20px;
  border-radius: 12px;
}

.task-form-section h3 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--fallback-p, oklch(var(--p)));
  margin: 0 0 20px 0;
}

.task-form {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-group label {
  font-weight: 500;
  color: var(--fallback-bc, oklch(var(--bc)));
  font-size: 0.9rem;
}

.form-group input {
  padding: 10px;
  border: 2px solid var(--fallback-b3, oklch(var(--b3)));
  border-radius: 6px;
  background: var(--fallback-b1, oklch(var(--b1)));
  color: var(--fallback-bc, oklch(var(--bc)));
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-group input:focus {
  outline: none;
  border-color: var(--fallback-p, oklch(var(--p)));
  box-shadow: 0 0 0 2px var(--fallback-p, oklch(var(--p) / 0.2));
}

.submit-button {
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border: none;
  padding: 12px 20px;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.9rem;
}

.submit-button:hover {
  background: var(--fallback-s, oklch(var(--s)));
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Floating Action Button */
.fab-home {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 60px;
  height: 60px;
  background: var(--fallback-p, oklch(var(--p)));
  color: var(--fallback-pc, oklch(var(--pc)));
  border: none;
  border-radius: 50%;
  font-weight: 700;
  font-size: 0.8rem;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  z-index: 1000;
}

.fab-home:hover {
  background: var(--fallback-s, oklch(var(--s)));
  transform: scale(1.1);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.4);
}

.fab-home:active {
  transform: scale(0.95);
}

/* Responsive Design untuk Tablet */
@media screen and (max-width: 1024px) {
  .calendar-container {
    flex-direction: column;
    gap: 15px;
    padding: 15px;
    height: 100vh;
    overflow-y: auto;
  }

  .main-calendar-section {
    flex: none;
  }

  .secondary-section {
    flex: none;
    flex-direction: row;
    gap: 15px;
  }

  .secondary-calendar {
    flex: 1;
  }

  .task-form-section {
    flex: 1;
  }

  .calendar-title {
    font-size: 1.5rem;
  }

  .calendar-date {
    min-height: 50px;
    padding: 10px 5px;
  }

  .task-list-section {
    flex: 0 0 150px;
  }
}

/* Responsive Design untuk Mobile */
@media screen and (max-width: 768px) {
  .calendar-container {
    padding: 10px;
    gap: 10px;
    height: 100vh;
    overflow-y: auto;
  }

  .secondary-section {
    flex-direction: column;
  }

  .calendar-title {
    font-size: 1.2rem;
  }

  .nav-button {
    width: 40px;
    height: 40px;
    font-size: 1.2rem;
  }

  .calendar-date {
    min-height: 40px;
    padding: 8px 4px;
    font-size: 0.9rem;
  }

  .day-header {
    padding: 8px;
    font-size: 0.8rem;
  }

  .task-list-section {
    flex: 0 0 120px;
    max-height: 120px;
    overflow-y: auto;
  }

  .fab-home {
    width: 50px;
    height: 50px;
    bottom: 20px;
    right: 20px;
    font-size: 0.7rem;
  }

  .secondary-calendar-grid {
    gap: 1px;
  }

  .secondary-date {
    min-height: 25px;
    padding: 5px 2px;
    font-size: 0.7rem;
  }

  .secondary-day-header {
    padding: 3px;
    font-size: 0.6rem;
  }

  /* Improve form layout on mobile */
  .task-form-section {
    padding: 15px;
  }

  .form-group input {
    padding: 12px;
    font-size: 16px; /* Prevent zoom on iOS */
  }

  .submit-button {
    padding: 12px 20px;
    font-size: 16px;
  }
}

/* Extra small screens */
@media screen and (max-width: 480px) {
  .calendar-container {
    padding: 5px;
  }

  .calendar-grid {
    padding: 5px;
    gap: 1px;
  }

  .calendar-date {
    min-height: 35px;
    padding: 5px 2px;
    font-size: 0.8rem;
  }

  .day-header {
    padding: 5px;
    font-size: 0.7rem;
  }

  .calendar-title {
    font-size: 1rem;
  }

  .nav-button {
    width: 35px;
    height: 35px;
    font-size: 1rem;
  }

  .task-form-section {
    padding: 15px;
  }

  .form-group input {
    padding: 8px;
    font-size: 0.8rem;
  }

  .submit-button {
    padding: 10px 15px;
    font-size: 0.8rem;
  }
}

/* Landscape orientation adjustments */
@media screen and (orientation: landscape) and (max-height: 500px) {
  .calendar-container {
    flex-direction: row;
    height: 100vh;
    overflow: hidden;
  }

  .main-calendar-section {
    flex: 0 0 65%;
  }

  .secondary-section {
    flex: 0 0 35%;
    flex-direction: column;
  }

  .task-list-section {
    flex: 0 0 100px;
  }

  .calendar-date {
    min-height: 30px;
    padding: 5px;
    font-size: 0.8rem;
  }

  .day-header {
    padding: 5px;
    font-size: 0.7rem;
  }
}
