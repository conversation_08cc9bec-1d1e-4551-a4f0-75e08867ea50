import React, { useState, useEffect } from 'react';
import './Calendar.css';
import { storage } from '../utils/storage';
import { notificationManager } from '../utils/notifications';

/**
 * Interface untuk task reminder
 */
export interface TaskReminder {
  id: string;
  taskName: string;
  date: string;
  time: string;
  timestamp: number;
  notified: boolean;
}

/**
 * Interface untuk calendar date
 */
interface CalendarDate {
  date: number;
  month: number;
  year: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  hasTasks: boolean;
}

/**
 * Komponen Calendar dengan fitur task reminder
 * Layout: 70% main calendar, 30% secondary calendar + form
 */
const Calendar: React.FC = () => {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [tasks, setTasks] = useState<TaskReminder[]>([]);
  const [taskForm, setTaskForm] = useState({
    taskName: '',
    date: '',
    time: ''
  });

  // Initialize component
  useEffect(() => {
    loadTasks();
    setupNotificationCheck();
    initializeNotifications();
  }, []);

  /**
   * Initialize notification system
   */
  const initializeNotifications = async () => {
    try {
      if (!notificationManager.hasPermission()) {
        await notificationManager.requestPermission();
      }
    } catch (error) {
      console.error('[Calendar] Error initializing notifications:', error);
    }
  };

  /**
   * Load tasks from IndexedDB
   */
  const loadTasks = async () => {
    try {
      const savedTasks = await storage.getTaskReminders();
      setTasks(savedTasks);
    } catch (error) {
      console.error('[Calendar] Error loading tasks:', error);
    }
  };

  /**
   * Save task to IndexedDB
   */
  const saveTask = async (task: TaskReminder) => {
    try {
      await storage.saveTaskReminder(task);
      const updatedTasks = [...tasks, task];
      setTasks(updatedTasks);
    } catch (error) {
      console.error('[Calendar] Error saving task:', error);
    }
  };

  /**
   * Update task in IndexedDB
   */
  const updateTask = async (updatedTask: TaskReminder) => {
    try {
      await storage.updateTaskReminder(updatedTask);
      const updatedTasks = tasks.map(t =>
        t.id === updatedTask.id ? updatedTask : t
      );
      setTasks(updatedTasks);
    } catch (error) {
      console.error('[Calendar] Error updating task:', error);
    }
  };

  /**
   * Delete task from IndexedDB
   */
  const deleteTask = async (taskId: string) => {
    try {
      await storage.deleteTaskReminder(taskId);
      const updatedTasks = tasks.filter(t => t.id !== taskId);
      setTasks(updatedTasks);

      // Cancel notification if it exists
      const numericId = parseInt(taskId.replace(/\D/g, '')) || Date.now();
      await notificationManager.cancelNotification(numericId);
    } catch (error) {
      console.error('[Calendar] Error deleting task:', error);
    }
  };

  /**
   * Setup notification checking interval
   */
  const setupNotificationCheck = () => {
    const checkInterval = setInterval(() => {
      checkForNotifications();
    }, 60000); // Check every minute

    return () => clearInterval(checkInterval);
  };

  /**
   * Check for tasks that need notifications
   */
  const checkForNotifications = () => {
    const now = new Date();
    const currentDateTime = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`;

    tasks.forEach(task => {
      const taskDateTime = `${task.date} ${task.time}`;
      if (taskDateTime === currentDateTime && !task.notified) {
        showNotification(task);
        // Mark as notified
        const updatedTask = { ...task, notified: true };
        updateTask(updatedTask);
      }
    });
  };

  /**
   * Show notification for task
   */
  const showNotification = async (task: TaskReminder) => {
    try {
      await notificationManager.showNotification({
        title: `Pengingat Tugas: ${task.taskName}`,
        body: `Waktu: ${task.time}`,
        id: parseInt(task.id.replace(/\D/g, '')) || Date.now(),
        extra: { taskId: task.id }
      });
    } catch (error) {
      console.error('[Calendar] Error showing notification:', error);
    }
  };

  /**
   * Generate calendar dates for a given month
   */
  const generateCalendarDates = (date: Date): CalendarDate[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    // const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const dates: CalendarDate[] = [];
    const today = new Date();

    for (let i = 0; i < 42; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);
      
      const dateString = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, '0')}-${String(currentDate.getDate()).padStart(2, '0')}`;
      const hasTasks = tasks.some(task => task.date === dateString);

      dates.push({
        date: currentDate.getDate(),
        month: currentDate.getMonth(),
        year: currentDate.getFullYear(),
        isCurrentMonth: currentDate.getMonth() === month,
        isToday: currentDate.toDateString() === today.toDateString(),
        hasTasks
      });
    }

    return dates;
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!taskForm.taskName || !taskForm.date || !taskForm.time) {
      alert('Mohon lengkapi semua field');
      return;
    }

    const newTask: TaskReminder = {
      id: `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      taskName: taskForm.taskName,
      date: taskForm.date,
      time: taskForm.time,
      timestamp: Date.now(),
      notified: false
    };

    await saveTask(newTask);

    // Schedule notification for the task
    try {
      const taskDateTime = new Date(`${newTask.date}T${newTask.time}`);
      if (taskDateTime > new Date()) {
        await notificationManager.scheduleNotification({
          title: `Pengingat Tugas: ${newTask.taskName}`,
          body: `Waktu: ${newTask.time}`,
          id: parseInt(newTask.id.replace(/\D/g, '')) || Date.now(),
          schedule: { at: taskDateTime },
          extra: { taskId: newTask.id }
        });
        console.log('[Calendar] Notification scheduled for:', taskDateTime);
      }
    } catch (error) {
      console.error('[Calendar] Error scheduling notification:', error);
    }

    // Reset form
    setTaskForm({
      taskName: '',
      date: '',
      time: ''
    });

    // Show success message
    if (window.confirm('Task berhasil ditambahkan! Apakah Anda ingin menambah task lain?')) {
      // User wants to add another task, form is already reset
    } else {
      // User is done, could navigate away or do something else
    }
  };

  /**
   * Navigate to previous month
   */
  const previousMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() - 1, 1));
  };

  /**
   * Navigate to next month
   */
  const nextMonth = () => {
    setCurrentDate(new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1));
  };

  /**
   * Navigate to home
   */
  const navigateToHome = () => {
    // This will be handled by parent component
    window.dispatchEvent(new CustomEvent('navigateToHome'));
  };

  const currentMonthDates = generateCalendarDates(currentDate);
  const nextMonthDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 1);
  const nextMonthDates = generateCalendarDates(nextMonthDate);

  const monthNames = [
    'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
    'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
  ];

  const dayNames = ['Min', 'Sen', 'Sel', 'Rab', 'Kam', 'Jum', 'Sab'];

  return (
    <div className="calendar-container">
      {/* Main Calendar Section (70%) */}
      <div className="main-calendar-section">
        {/* Calendar Header */}
        <div className="calendar-header">
          <button className="nav-button" onClick={previousMonth}>
            &#8249;
          </button>
          <h2 className="calendar-title">
            {monthNames[currentDate.getMonth()]} {currentDate.getFullYear()}
          </h2>
          <button className="nav-button" onClick={nextMonth}>
            &#8250;
          </button>
        </div>

        {/* Calendar Grid */}
        <div className="calendar-grid">
          {/* Day headers */}
          {dayNames.map(day => (
            <div key={day} className="day-header">
              {day}
            </div>
          ))}
          
          {/* Calendar dates */}
          {currentMonthDates.map((dateObj, index) => (
            <div
              key={index}
              className={`calendar-date ${
                !dateObj.isCurrentMonth ? 'other-month' : ''
              } ${dateObj.isToday ? 'today' : ''} ${
                dateObj.hasTasks ? 'has-tasks' : ''
              }`}
            >
              {dateObj.date}
              {dateObj.hasTasks && <div className="task-indicator"></div>}
            </div>
          ))}
        </div>

        {/* Task List */}
        <div className="task-list-section">
          <h3>Daftar Tugas</h3>
          <div className="task-list">
            {tasks.length === 0 ? (
              <p className="no-tasks">Belum ada tugas yang dijadwalkan</p>
            ) : (
              tasks
                .sort((a, b) => new Date(`${a.date} ${a.time}`).getTime() - new Date(`${b.date} ${b.time}`).getTime())
                .map(task => (
                  <div key={task.id} className="task-item">
                    <div className="task-info">
                      <h4>{task.taskName}</h4>
                      <p>{task.date} - {task.time}</p>
                    </div>
                    <div className="task-actions">
                      <div className={`task-status ${task.notified ? 'notified' : 'pending'}`}>
                        {task.notified ? '✓' : '⏰'}
                      </div>
                      <button
                        className="delete-task-btn"
                        onClick={() => {
                          if (window.confirm('Apakah Anda yakin ingin menghapus task ini?')) {
                            deleteTask(task.id);
                          }
                        }}
                        title="Hapus task"
                      >
                        🗑️
                      </button>
                    </div>
                  </div>
                ))
            )}
          </div>
        </div>
      </div>

      {/* Secondary Section (30%) */}
      <div className="secondary-section">
        {/* Next Month Calendar */}
        <div className="secondary-calendar">
          <h3 className="secondary-title">
            {monthNames[nextMonthDate.getMonth()]} {nextMonthDate.getFullYear()}
          </h3>
          <div className="secondary-calendar-grid">
            {dayNames.map(day => (
              <div key={day} className="secondary-day-header">
                {day.charAt(0)}
              </div>
            ))}
            {nextMonthDates.map((dateObj, index) => (
              <div
                key={index}
                className={`secondary-date ${
                  !dateObj.isCurrentMonth ? 'other-month' : ''
                } ${dateObj.isToday ? 'today' : ''} ${
                  dateObj.hasTasks ? 'has-tasks' : ''
                }`}
              >
                {dateObj.date}
              </div>
            ))}
          </div>
        </div>

        {/* Task Form */}
        <div className="task-form-section">
          <h3>Tambah Pengingat</h3>
          <form onSubmit={handleSubmit} className="task-form">
            <div className="form-group">
              <label htmlFor="taskName">Nama Tugas</label>
              <input
                type="text"
                id="taskName"
                value={taskForm.taskName}
                onChange={(e) => setTaskForm({...taskForm, taskName: e.target.value})}
                placeholder="Masukkan nama tugas"
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="taskDate">Tanggal</label>
              <input
                type="date"
                id="taskDate"
                value={taskForm.date}
                onChange={(e) => setTaskForm({...taskForm, date: e.target.value})}
                required
              />
            </div>
            
            <div className="form-group">
              <label htmlFor="taskTime">Waktu</label>
              <input
                type="time"
                id="taskTime"
                value={taskForm.time}
                onChange={(e) => setTaskForm({...taskForm, time: e.target.value})}
                required
              />
            </div>
            
            <button type="submit" className="submit-button">
              Tambah Tugas
            </button>
          </form>
        </div>
      </div>

      {/* Floating Action Button */}
      <button className="fab-home" onClick={navigateToHome}>
        HOME
      </button>
    </div>
  );
};

export default Calendar;
